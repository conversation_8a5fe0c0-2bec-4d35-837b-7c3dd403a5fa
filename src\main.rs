use clap::{Arg, Command};
use colored::*;
use notify::{Config, Event, RecommendedWatcher, RecursiveMode, Watcher};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::mpsc;
use std::time::{Duration, SystemTime};
use tokio::time::sleep;
use walkdir::WalkDir;

mod patterns;
mod scanner;
mod watcher;

use patterns::TelemetryPatterns;
use scanner::FileScanner;
use watcher::FileWatcher;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Detection {
    pub file_path: PathBuf,
    pub line_number: usize,
    pub content: String,
    pub pattern_type: String,
    pub severity: String,
    pub timestamp: SystemTime,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ScanResult {
    pub detections: Vec<Detection>,
    pub scan_time: SystemTime,
    pub files_scanned: usize,
    pub directories_scanned: usize,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let matches = Command::new("Telemetry & Machine ID Watcher")
        .version("1.0")
        .author("Security Scanner")
        .about("Watches and scans for telemetry and machine ID related code")
        .arg(
            Arg::new("path")
                .short('p')
                .long("path")
                .value_name("PATH")
                .help("Path to scan (default: C:\\Users)")
                .default_value("C:\\Users"),
        )
        .arg(
            Arg::new("watch")
                .short('w')
                .long("watch")
                .help("Enable file watching mode")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("output")
                .short('o')
                .long("output")
                .value_name("FILE")
                .help("Output results to JSON file"),
        )
        .arg(
            Arg::new("extensions")
                .short('e')
                .long("extensions")
                .value_name("EXTS")
                .help("File extensions to scan (comma-separated)")
                .default_value("rs,py,js,ts,cpp,c,h,hpp,cs,java,go"),
        )
        .get_matches();

    let scan_path = matches.get_one::<String>("path").unwrap();
    let watch_mode = matches.get_flag("watch");
    let output_file = matches.get_one::<String>("output");
    let extensions: Vec<&str> = matches
        .get_one::<String>("extensions")
        .unwrap()
        .split(',')
        .collect();

    println!("{}", "🔍 Telemetry & Machine ID Watcher".bright_cyan().bold());
    println!("Scanning path: {}", scan_path.bright_yellow());
    println!("Extensions: {}", extensions.join(", ").bright_green());

    let patterns = TelemetryPatterns::new();
    let scanner = FileScanner::new(patterns, extensions);

    // Initial scan
    println!("\n{}", "📊 Starting initial scan...".bright_blue());
    let scan_result = scanner.scan_directory(Path::new(scan_path)).await?;
    
    display_results(&scan_result);

    if let Some(output_path) = output_file {
        save_results(&scan_result, output_path)?;
        println!("Results saved to: {}", output_path.bright_green());
    }

    if watch_mode {
        println!("\n{}", "👁️  Starting file watcher...".bright_magenta());
        let watcher = FileWatcher::new(scanner);
        watcher.watch(Path::new(scan_path)).await?;
    }

    Ok(())
}

fn display_results(result: &ScanResult) {
    println!("\n{}", "📋 SCAN RESULTS".bright_cyan().bold());
    println!("Files scanned: {}", result.files_scanned.to_string().bright_yellow());
    println!("Directories scanned: {}", result.directories_scanned.to_string().bright_yellow());
    println!("Detections found: {}", result.detections.len().to_string().bright_red().bold());

    if !result.detections.is_empty() {
        println!("\n{}", "🚨 DETECTIONS:".bright_red().bold());
        
        let mut by_type: HashMap<String, Vec<&Detection>> = HashMap::new();
        for detection in &result.detections {
            by_type.entry(detection.pattern_type.clone())
                .or_insert_with(Vec::new)
                .push(detection);
        }

        for (pattern_type, detections) in by_type {
            println!("\n{} ({})", pattern_type.bright_yellow().bold(), detections.len());
            for detection in detections.iter().take(10) { // Limit to first 10 per type
                let severity_color = match detection.severity.as_str() {
                    "HIGH" => "red",
                    "MEDIUM" => "yellow",
                    "LOW" => "green",
                    _ => "white",
                };
                
                println!("  {} {}:{}",
                    detection.severity.color(severity_color).bold(),
                    detection.file_path.display().to_string().bright_blue(),
                    detection.line_number.to_string().bright_white()
                );
                println!("    {}", detection.content.trim().bright_white());
            }
            if detections.len() > 10 {
                println!("    ... and {} more", (detections.len() - 10).to_string().bright_cyan());
            }
        }
    }
}

fn save_results(result: &ScanResult, output_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let json = serde_json::to_string_pretty(result)?;
    fs::write(output_path, json)?;
    Ok(())
}
