use regex::Regex;
use std::collections::HashMap;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PatternInfo {
    pub regex: Regex,
    pub description: String,
    pub severity: String,
    pub category: String,
}

#[derive(Debug)]
pub struct TelemetryPatterns {
    pub patterns: HashMap<String, PatternInfo>,
}

impl TelemetryPatterns {
    pub fn new() -> Self {
        let mut patterns = HashMap::new();

        // Machine ID patterns
        Self::add_pattern(&mut patterns, "machine_id_generation", 
            r"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)",
            "Machine/Device ID generation or usage",
            "HIGH",
            "MACHINE_ID"
        );

        Self::add_pattern(&mut patterns, "uuid_generation",
            r"(?i)(uuid::v[1-5]|Uuid::new|uuid\.new|generate[_-]?uuid)",
            "UUID generation for identification",
            "MEDIUM",
            "MACHINE_ID"
        );

        Self::add_pattern(&mut patterns, "mac_address",
            r"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)",
            "MAC address collection",
            "HIGH",
            "MACHINE_ID"
        );

        Self::add_pattern(&mut patterns, "cpu_info",
            r"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)",
            "CPU information collection",
            "HIGH",
            "MACHINE_ID"
        );

        Self::add_pattern(&mut patterns, "disk_serial",
            r"(?i)(disk[_-]?serial|drive[_-]?id|volume[_-]?serial)",
            "Disk/Drive serial number collection",
            "HIGH",
            "MACHINE_ID"
        );

        // Telemetry patterns
        Self::add_pattern(&mut patterns, "telemetry_collection",
            r"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)",
            "Telemetry/Analytics collection",
            "HIGH",
            "TELEMETRY"
        );

        Self::add_pattern(&mut patterns, "data_transmission",
            r"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)",
            "Data transmission to external servers",
            "HIGH",
            "TELEMETRY"
        );

        Self::add_pattern(&mut patterns, "user_behavior",
            r"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)",
            "User behavior tracking",
            "MEDIUM",
            "TELEMETRY"
        );

        Self::add_pattern(&mut patterns, "crash_reporting",
            r"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)",
            "Crash/Error reporting",
            "MEDIUM",
            "TELEMETRY"
        );

        // System information patterns
        Self::add_pattern(&mut patterns, "system_info",
            r"(?i)(system[_-]?info|os[_-]?version|platform[_-]?info|environment[_-]?info)",
            "System information collection",
            "MEDIUM",
            "SYSTEM_INFO"
        );

        Self::add_pattern(&mut patterns, "registry_access",
            r"(?i)(registry|hkey_|regkey|winreg)",
            "Windows Registry access",
            "HIGH",
            "SYSTEM_INFO"
        );

        Self::add_pattern(&mut patterns, "wmi_queries",
            r"(?i)(wmi|win32_|cim_|select.*from.*win32)",
            "WMI queries for system information",
            "HIGH",
            "SYSTEM_INFO"
        );

        // Network patterns
        Self::add_pattern(&mut patterns, "network_requests",
            r"(?i)(http[s]?://|api[_-]?call|rest[_-]?client|web[_-]?request)",
            "Network requests (potential data transmission)",
            "MEDIUM",
            "NETWORK"
        );

        Self::add_pattern(&mut patterns, "external_domains",
            r"(?i)(\.com|\.net|\.org|\.io|analytics|telemetry|tracking)",
            "External domain references",
            "LOW",
            "NETWORK"
        );

        // File system patterns
        Self::add_pattern(&mut patterns, "user_directories",
            r"(?i)(users/|\\users\\|home/|appdata|documents|desktop)",
            "User directory access",
            "MEDIUM",
            "FILESYSTEM"
        );

        Self::add_pattern(&mut patterns, "config_files",
            r"(?i)(\.config|\.settings|\.ini|\.json|\.xml|\.plist)",
            "Configuration file access",
            "LOW",
            "FILESYSTEM"
        );

        // Rust-specific patterns
        Self::add_pattern(&mut patterns, "rust_sysinfo",
            r"(?i)(sysinfo::|System::new|get_processor|get_networks)",
            "Rust sysinfo crate usage",
            "HIGH",
            "RUST_SPECIFIC"
        );

        Self::add_pattern(&mut patterns, "rust_machine_uid",
            r"(?i)(machine_uid|get_machine_id)",
            "Rust machine-uid crate usage",
            "HIGH",
            "RUST_SPECIFIC"
        );

        Self::add_pattern(&mut patterns, "rust_reqwest",
            r"(?i)(reqwest::|Client::new|post|get.*http)",
            "Rust reqwest HTTP client usage",
            "MEDIUM",
            "RUST_SPECIFIC"
        );

        TelemetryPatterns { patterns }
    }

    fn add_pattern(
        patterns: &mut HashMap<String, PatternInfo>,
        name: &str,
        pattern: &str,
        description: &str,
        severity: &str,
        category: &str,
    ) {
        if let Ok(regex) = Regex::new(pattern) {
            patterns.insert(
                name.to_string(),
                PatternInfo {
                    regex,
                    description: description.to_string(),
                    severity: severity.to_string(),
                    category: category.to_string(),
                },
            );
        }
    }

    pub fn scan_content(&self, content: &str) -> Vec<(String, &PatternInfo, Vec<usize>)> {
        let mut matches = Vec::new();
        
        for (name, pattern_info) in &self.patterns {
            let line_matches: Vec<usize> = content
                .lines()
                .enumerate()
                .filter_map(|(line_num, line)| {
                    if pattern_info.regex.is_match(line) {
                        Some(line_num + 1)
                    } else {
                        None
                    }
                })
                .collect();

            if !line_matches.is_empty() {
                matches.push((name.clone(), pattern_info, line_matches));
            }
        }

        matches
    }
}
