use crate::{<PERSON><PERSON><PERSON>, ScanR<PERSON>ult, TelemetryPatterns};
use std::fs;
use std::path::Path;
use std::time::SystemTime;
use tokio::task;
use walkdir::WalkDir;

pub struct FileScanner {
    patterns: TelemetryPatterns,
    extensions: Vec<String>,
}

impl FileScanner {
    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {
        Self {
            patterns,
            extensions: extensions.iter().map(|s| s.to_string()).collect(),
        }
    }

    pub async fn scan_directory(&self, path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {
        let mut detections = Vec::new();
        let mut files_scanned = 0;
        let mut directories_scanned = 0;
        let scan_time = SystemTime::now();

        println!("🔍 Scanning directory: {}", path.display());

        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_dir() {
                directories_scanned += 1;
                continue;
            }

            let file_path = entry.path();
            
            // Check if file has one of the target extensions
            if let Some(extension) = file_path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if !self.extensions.contains(&ext_str.to_lowercase()) {
                        continue;
                    }
                } else {
                    continue;
                }
            } else {
                continue;
            }

            files_scanned += 1;
            
            // Scan file content
            match self.scan_file(file_path).await {
                Ok(mut file_detections) => {
                    detections.append(&mut file_detections);
                }
                Err(e) => {
                    eprintln!("Error scanning {}: {}", file_path.display(), e);
                }
            }

            // Print progress every 100 files
            if files_scanned % 100 == 0 {
                println!("📄 Scanned {} files, {} detections so far", files_scanned, detections.len());
            }
        }

        Ok(ScanResult {
            detections,
            scan_time,
            files_scanned,
            directories_scanned,
        })
    }

    pub async fn scan_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {
        let content = task::spawn_blocking({
            let file_path = file_path.to_owned();
            move || fs::read_to_string(&file_path)
        }).await??;

        let mut detections = Vec::new();
        let matches = self.patterns.scan_content(&content);

        for (pattern_name, pattern_info, line_numbers) in matches {
            for line_number in line_numbers {
                let line_content = content
                    .lines()
                    .nth(line_number - 1)
                    .unwrap_or("")
                    .to_string();

                detections.push(Detection {
                    file_path: file_path.to_path_buf(),
                    line_number,
                    content: line_content,
                    pattern_type: format!("{} ({})", pattern_info.description, pattern_info.category),
                    severity: pattern_info.severity.clone(),
                    timestamp: SystemTime::now(),
                });
            }
        }

        Ok(detections)
    }
}
